// electron/services/llm-service.ts
import { ipcMain } from 'electron';
import { withTimeout, TimeoutError, isTimeoutError } from '../utils/timeout';
import { ConcurrencyManager } from '../utils/concurrency-manager';
import { setGlobalDebugMode, debugLLM, debugNetwork, debugTiming, debugError } from '../utils/debug';
import { setGlobalTelemetryEnabled, trackLLMEvent, trackPerformance, trackError } from '../utils/telemetry';

// Note: Using Node.js built-in fetch (available in Node.js 18+)
// This avoids ES module import issues with node-fetch v3+
declare const fetch: typeof globalThis.fetch;

// Import updated Anthropic models
const ANTHROPIC_MODEL_MAP = {
  // Claude 4 Series (Latest Generation)
  'opus-4': 'claude-opus-4-20250514',
  'sonnet-4': 'claude-sonnet-4-20250514',

  // Claude 3.7 Series (Enhanced 3.5)
  'sonnet-3.7': 'claude-3-7-sonnet-20250219',

  // Claude 3.5 Series (Current Production)
  'haiku-3.5': 'claude-3-5-haiku-20241022',
  'sonnet-3.5-v2': 'claude-3-5-sonnet-20241022',
  'sonnet-3.5': 'claude-3-5-sonnet-20240620',

  // Claude 3 Series (Stable Production)
  'opus-3': 'claude-3-opus-20240229',
  'sonnet-3': 'claude-3-sonnet-20240229',
  'haiku-3': 'claude-3-haiku-20240307',

  // Legacy mappings for backward compatibility
  'claude-3-opus': 'claude-3-opus-20240229',
  'claude-3-sonnet': 'claude-3-sonnet-20240229',
  'claude-3-haiku': 'claude-3-haiku-20240307',
  'claude-3.5-sonnet': 'claude-3-5-sonnet-20241022',

  // Direct ID mappings
  'claude-opus-4-20250514': 'claude-opus-4-20250514',
  'claude-sonnet-4-20250514': 'claude-sonnet-4-20250514',
  'claude-3-7-sonnet-20250219': 'claude-3-7-sonnet-20250219',
  'claude-3-5-haiku-20241022': 'claude-3-5-haiku-20241022',
  'claude-3-5-sonnet-20241022': 'claude-3-5-sonnet-20241022',
  'claude-3-5-sonnet-20240620': 'claude-3-5-sonnet-20240620',
  'claude-3-opus-20240229': 'claude-3-opus-20240229',
  'claude-3-sonnet-20240229': 'claude-3-sonnet-20240229',
  'claude-3-haiku-20240307': 'claude-3-haiku-20240307'
};

export type LLMProvider = 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks';

interface ProviderConfig {
  name: string;
  apiUrl: string;
  modelMap: Record<string, string>;
  headers: (apiKey: string) => Record<string, string>;
  requestFormat: 'openai' | 'anthropic' | 'custom';
  responseFormat: 'openai' | 'anthropic' | 'custom';
  keyValidationEndpoint?: string;
}

interface LLMRequest {
  messages: Array<{ role: string; content: string }>;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

interface LLMResponse {
  content: string;
  tokensUsed: {
    input: number;
    output: number;
    total: number;
  };
  model: string;
  finishReason: string;
}

const LLMProviderRegistry: Record<LLMProvider, ProviderConfig> = {
  openai: {
    name: 'OpenAI',
    apiUrl: 'https://api.openai.com/v1/chat/completions',
    modelMap: {
      'gpt-4': 'gpt-4',
      'gpt-4-turbo': 'gpt-4-turbo-preview',
      'gpt-3.5-turbo': 'gpt-3.5-turbo',
      'gpt-4o': 'gpt-4o',
      'gpt-4o-mini': 'gpt-4o-mini'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    keyValidationEndpoint: 'https://api.openai.com/v1/models'
  },

  anthropic: {
    name: 'Anthropic',
    apiUrl: 'https://api.anthropic.com/v1/messages',
    modelMap: ANTHROPIC_MODEL_MAP,
    headers: (apiKey: string) => ({
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01',
      'Content-Type': 'application/json'
    }),
    requestFormat: 'anthropic',
    responseFormat: 'anthropic',
    keyValidationEndpoint: 'https://api.anthropic.com/v1/messages'
  },

  openrouter: {
    name: 'OpenRouter',
    apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
    modelMap: {
      'mixtral': 'mistralai/mixtral-8x7b-instruct',
      'llama-3.1-70b': 'meta-llama/llama-3.1-70b-instruct',
      'claude-3-sonnet': 'anthropic/claude-3-sonnet',
      'gpt-4': 'openai/gpt-4',
      'gemini-pro': 'google/gemini-pro'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://synapse.dev',
      'X-Title': 'Synapse Agent System'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    keyValidationEndpoint: 'https://openrouter.ai/api/v1/models'
  },

  azure: {
    name: 'Azure OpenAI',
    apiUrl: 'https://{resource}.openai.azure.com/openai/deployments/{deployment}/chat/completions?api-version=2024-02-15-preview',
    modelMap: {
      'gpt-4': 'gpt-4',
      'gpt-35-turbo': 'gpt-35-turbo',
      'gpt-4-turbo': 'gpt-4-turbo'
    },
    headers: (apiKey: string) => ({
      'api-key': apiKey,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai'
  },

  google: {
    name: 'Google AI',
    apiUrl: 'https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent',
    modelMap: {
      'gemini-pro': 'gemini-pro',
      'gemini-1.5-pro': 'gemini-1.5-pro',
      'gemini-1.5-flash': 'gemini-1.5-flash'
    },
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json'
    }),
    requestFormat: 'custom',
    responseFormat: 'custom',
    keyValidationEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models'
  },

  deepseek: {
    name: 'DeepSeek',
    apiUrl: 'https://api.deepseek.com/chat/completions',
    modelMap: {
      'deepseek-chat': 'deepseek-chat',
      'deepseek-coder': 'deepseek-coder'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    keyValidationEndpoint: 'https://api.deepseek.com/models'
  },

  fireworks: {
    name: 'Fireworks AI',
    apiUrl: 'https://api.fireworks.ai/inference/v1/chat/completions',
    modelMap: {
      'llama-3.1-70b': 'accounts/fireworks/models/llama-v3p1-70b-instruct',
      'mixtral-8x7b': 'accounts/fireworks/models/mixtral-8x7b-instruct',
      'qwen-72b': 'accounts/fireworks/models/qwen2-72b-instruct'
    },
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai',
    responseFormat: 'openai',
    keyValidationEndpoint: 'https://api.fireworks.ai/inference/v1/models'
  }
};

export class LLMService {
  private concurrencyManager: ConcurrencyManager;

  constructor() {
    // Ensure fetch is available (Node.js 18+ built-in fetch)
    if (typeof fetch === 'undefined') {
      console.error('LLMService: fetch is not available. Node.js 18+ is required.');
      throw new Error('fetch is not available. Please upgrade to Node.js 18 or later.');
    }

    // ✅ Initialize with default concurrency limit (will be updated from settings)
    this.concurrencyManager = new ConcurrencyManager(3);
    this.registerIPCHandlers();
    debugLLM('LLMService initialized with Node.js built-in fetch and concurrency control');
  }

  /**
   * ✅ Update concurrency limit from settings
   */
  updateConcurrencyLimit(newLimit: number): void {
    this.concurrencyManager.setLimit(newLimit);
  }

  /**
   * ✅ Get concurrency statistics
   */
  getConcurrencyStats() {
    return this.concurrencyManager.getStats();
  }

  /**
   * ✅ Update debug mode from settings
   */
  updateDebugMode(enabled: boolean): void {
    setGlobalDebugMode(enabled);
    debugLLM('Debug mode updated:', enabled);
  }

  /**
   * ✅ Update telemetry mode from settings
   */
  updateTelemetryMode(enabled: boolean): void {
    setGlobalTelemetryEnabled(enabled);
    debugLLM('Telemetry mode updated:', enabled);
  }

  private registerIPCHandlers() {
    ipcMain.handle('llm:validateApiKey', async (_, provider: LLMProvider, apiKey: string, timeoutMs?: number) => {
      const timeout = timeoutMs || 10000; // Default 10s for validation
      const startTime = Date.now();

      debugLLM(`Starting API key validation for ${provider}`);
      trackLLMEvent(provider, 'validate_api_key_started');

      try {
        // ✅ Wrap with both concurrency control and timeout
        const result = await this.concurrencyManager.run(
          () => withTimeout(
            this.validateApiKey(provider, apiKey),
            timeout,
            `${provider} API key validation`
          ),
          {
            label: `${provider} API key validation`,
            priority: 'high',
            timeout
          }
        );

        const duration = Date.now() - startTime;
        debugTiming(`${provider} API key validation`, startTime);
        trackLLMEvent(provider, 'validate_api_key_completed', { duration });
        trackPerformance('llm_api_key_validation', duration, 'ms', provider);

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        debugError(error, `${provider} API key validation`);
        trackLLMEvent(provider, 'validate_api_key_failed', { duration });
        trackError('llm_api_key_validation_failed', provider);
        throw error;
      }
    });

    ipcMain.handle('llm:callLLM', async (_, provider: LLMProvider, request: LLMRequest, apiKey: string, timeoutMs?: number) => {
      const timeout = timeoutMs || 30000; // Default 30s for LLM calls
      const startTime = Date.now();

      debugLLM(`Starting LLM call to ${provider}/${request.model}`);
      trackLLMEvent(provider, 'llm_call_started', { model: request.model });

      try {
        // ✅ Wrap with both concurrency control and timeout
        const result = await this.concurrencyManager.run(
          () => withTimeout(
            this.callLLM(provider, request, apiKey),
            timeout,
            `${provider} ${request.model} completion`
          ),
          {
            label: `${provider} ${request.model} completion`,
            priority: 'high',
            timeout
          }
        );

        const duration = Date.now() - startTime;
        debugTiming(`${provider}/${request.model} completion`, startTime);
        trackLLMEvent(provider, 'llm_call_completed', {
          model: request.model,
          duration,
          tokensUsed: result.tokensUsed?.total || 0
        });
        trackPerformance('llm_call_duration', duration, 'ms', `${provider}/${request.model}`);

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        debugError(error, `${provider}/${request.model} completion`);
        trackLLMEvent(provider, 'llm_call_failed', { model: request.model, duration });
        trackError('llm_call_failed', `${provider}/${request.model}`);
        throw error;
      }
    });

    ipcMain.handle('llm:fetchModels', async (_, provider: LLMProvider, apiKey: string, timeoutMs?: number) => {
      const timeout = timeoutMs || 15000; // Default 15s for model fetching

      // ✅ Wrap with both concurrency control and timeout
      return this.concurrencyManager.run(
        () => withTimeout(
          this.fetchModels(provider, apiKey),
          timeout,
          `${provider} model fetching`
        ),
        {
          label: `${provider} model fetching`,
          priority: 'medium',
          timeout
        }
      );
    });

    // ✅ Add IPC handler to update concurrency limit
    ipcMain.handle('llm:updateConcurrencyLimit', async (_, newLimit: number) => {
      this.updateConcurrencyLimit(newLimit);
      return { success: true, newLimit };
    });

    // ✅ Add IPC handler to get concurrency stats
    ipcMain.handle('llm:getConcurrencyStats', async () => {
      return this.getConcurrencyStats();
    });

    // ✅ Add IPC handler to update debug mode
    ipcMain.handle('llm:updateDebugMode', async (_, enabled: boolean) => {
      this.updateDebugMode(enabled);
      return { success: true, debugMode: enabled };
    });

    // ✅ Add IPC handler to update telemetry mode
    ipcMain.handle('llm:updateTelemetryMode', async (_, enabled: boolean) => {
      this.updateTelemetryMode(enabled);
      return { success: true, telemetryEnabled: enabled };
    });
  }

  private async validateApiKey(provider: LLMProvider, apiKey: string): Promise<boolean> {
    const config = LLMProviderRegistry[provider];

    if (!config.keyValidationEndpoint) {
      return apiKey.length > 0;
    }

    try {
      let response: any;

      switch (provider) {
        case 'openai':
        case 'openrouter':
        case 'deepseek':
        case 'fireworks':
          // These providers support GET requests to their models endpoint
          response = await fetch(config.keyValidationEndpoint, {
            method: 'GET',
            headers: config.headers(apiKey)
          });
          break;

        case 'anthropic':
          // Anthropic requires a POST request with minimal payload for validation
          response = await fetch(config.keyValidationEndpoint, {
            method: 'POST',
            headers: config.headers(apiKey),
            body: JSON.stringify({
              model: 'claude-3-haiku-20240307',
              max_tokens: 1,
              messages: [{ role: 'user', content: 'test' }]
            })
          });
          break;

        case 'google':
          // Google AI uses API key in URL parameter
          const googleUrl = `${config.keyValidationEndpoint}?key=${apiKey}`;
          response = await fetch(googleUrl, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
          });
          break;

        default:
          // Fallback to GET request
          response = await fetch(config.keyValidationEndpoint, {
            method: 'GET',
            headers: config.headers(apiKey)
          });
          break;
      }

      // For Anthropic, we expect either success or a specific error that indicates valid auth
      if (provider === 'anthropic') {
        // Valid API key will return 200 or 400 (bad request due to minimal payload)
        // Invalid API key will return 401 (unauthorized)
        const isValid = response.status === 200 || response.status === 400;
        console.log(`Anthropic API key validation: status ${response.status}, valid: ${isValid}`);
        return isValid;
      }

      const isValid = response.ok;
      console.log(`${provider} API key validation: status ${response.status}, valid: ${isValid}`);
      return isValid;
    } catch (error) {
      console.error(`API key validation failed for ${provider}:`, error);
      return false;
    }
  }

  private async callLLM(provider: LLMProvider, request: LLMRequest, apiKey: string): Promise<LLMResponse> {
    const config = LLMProviderRegistry[provider];

    try {
      let response: any;

      switch (config.requestFormat) {
        case 'openai':
          response = await this.makeOpenAIRequest(config, apiKey, request);
          break;
        case 'anthropic':
          response = await this.makeAnthropicRequest(config, apiKey, request);
          break;
        case 'custom':
          response = await this.makeCustomRequest(provider, config, apiKey, request);
          break;
        default:
          throw new Error(`Unsupported request format: ${config.requestFormat}`);
      }

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      return this.parseResponse(provider, responseData);

    } catch (error) {
      console.error(`LLM request failed for ${provider}:`, error);
      throw error;
    }
  }

  private async makeOpenAIRequest(config: ProviderConfig, apiKey: string, request: LLMRequest): Promise<any> {
    const payload = {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens,
      stream: request.stream
    };

    return fetch(config.apiUrl, {
      method: 'POST',
      headers: config.headers(apiKey),
      body: JSON.stringify(payload)
    });
  }

  private async makeAnthropicRequest(config: ProviderConfig, apiKey: string, request: LLMRequest): Promise<any> {
    const systemMessage = request.messages.find(m => m.role === 'system');
    const conversationMessages = request.messages.filter(m => m.role !== 'system');

    const payload = {
      model: request.model,
      max_tokens: request.maxTokens,
      temperature: request.temperature,
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      }))
    };

    return fetch(config.apiUrl, {
      method: 'POST',
      headers: config.headers(apiKey),
      body: JSON.stringify(payload)
    });
  }

  private async makeCustomRequest(provider: LLMProvider, config: ProviderConfig, apiKey: string, request: LLMRequest): Promise<any> {
    if (provider === 'google') {
      const url = config.apiUrl.replace('{model}', request.model) + `?key=${apiKey}`;

      const payload = {
        contents: [{
          parts: [{
            text: request.messages.map(m => `${m.role}: ${m.content}`).join('\n\n')
          }]
        }],
        generationConfig: {
          temperature: request.temperature,
          maxOutputTokens: request.maxTokens
        }
      };

      return fetch(url, {
        method: 'POST',
        headers: config.headers(apiKey),
        body: JSON.stringify(payload)
      });
    }

    throw new Error(`Custom request format not implemented for provider: ${provider}`);
  }

  private parseResponse(provider: LLMProvider, data: any): LLMResponse {
    const config = LLMProviderRegistry[provider];

    switch (config.responseFormat) {
      case 'openai':
        return this.parseOpenAIResponse(data);
      case 'anthropic':
        return this.parseAnthropicResponse(data);
      case 'custom':
        return this.parseCustomResponse(provider, data);
      default:
        throw new Error(`Unsupported response format: ${config.responseFormat}`);
    }
  }

  private parseOpenAIResponse(data: any): LLMResponse {
    const choice = data.choices?.[0];
    const usage = data.usage;

    return {
      content: choice?.message?.content || '',
      tokensUsed: {
        input: usage?.prompt_tokens || 0,
        output: usage?.completion_tokens || 0,
        total: usage?.total_tokens || 0
      },
      model: data.model,
      finishReason: choice?.finish_reason || 'stop'
    };
  }

  private parseAnthropicResponse(data: any): LLMResponse {
    const content = data.content?.[0]?.text || '';
    const usage = data.usage;

    return {
      content,
      tokensUsed: {
        input: usage?.input_tokens || 0,
        output: usage?.output_tokens || 0,
        total: (usage?.input_tokens || 0) + (usage?.output_tokens || 0)
      },
      model: data.model,
      finishReason: data.stop_reason || 'stop'
    };
  }

  private parseCustomResponse(provider: LLMProvider, data: any): LLMResponse {
    if (provider === 'google') {
      const candidate = data.candidates?.[0];
      const content = candidate?.content?.parts?.[0]?.text || '';

      return {
        content,
        tokensUsed: {
          input: data.usageMetadata?.promptTokenCount || 0,
          output: data.usageMetadata?.candidatesTokenCount || 0,
          total: data.usageMetadata?.totalTokenCount || 0
        },
        model: data.model || 'gemini-pro',
        finishReason: candidate?.finishReason?.toLowerCase() || 'stop'
      };
    }

    throw new Error(`Custom response parsing not implemented for provider: ${provider}`);
  }

  private async fetchModels(provider: LLMProvider, apiKey: string): Promise<string[]> {
    const config = LLMProviderRegistry[provider];

    try {
      let response: any;

      switch (provider) {
        case 'openai':
          response = await fetch('https://api.openai.com/v1/models', {
            headers: config.headers(apiKey)
          });
          break;
        case 'openrouter':
          response = await fetch('https://openrouter.ai/api/v1/models', {
            headers: config.headers(apiKey)
          });
          break;
        case 'google':
          response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
            headers: { 'Content-Type': 'application/json' }
          });
          break;
        case 'deepseek':
          response = await fetch('https://api.deepseek.com/models', {
            headers: config.headers(apiKey)
          });
          break;
        case 'fireworks':
          response = await fetch('https://api.fireworks.ai/inference/v1/models', {
            headers: config.headers(apiKey)
          });
          break;
        default:
          throw new Error(`Model fetching not supported for provider: ${provider}`);
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.parseModelsResponse(provider, data);

    } catch (error) {
      console.error(`Model fetching failed for ${provider}:`, error);
      throw error;
    }
  }

  private parseModelsResponse(provider: LLMProvider, data: any): string[] {
    switch (provider) {
      case 'openai':
      case 'deepseek':
      case 'fireworks':
        return data.data?.map((model: any) => model.id) || [];
      case 'openrouter':
        return data.data?.map((model: any) => model.id) || [];
      case 'google':
        return data.models?.map((model: any) => model.name.split('/').pop()) || [];
      default:
        return [];
    }
  }
}
