// components/middleware/error-resolution-coordinator.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse, AgentMessage } from '../agents/agent-base';

export interface ErrorAnalysis {
  errorId: string;
  originalError: string;
  errorType: 'syntax' | 'runtime' | 'logic' | 'dependency' | 'configuration' | 'integration';
  severity: 'low' | 'medium' | 'high' | 'critical';
  context: string;
  previousAttempts: string[];
  rootCause?: string;
  affectedComponents: string[];
  estimatedComplexity: number; // 1-10
}

export interface ResolutionStrategy {
  id: string;
  name: string;
  description: string;
  approach: 'direct_fix' | 'workaround' | 'refactor' | 'alternative_implementation' | 'configuration_change';
  estimatedEffort: number; // 1-10
  successProbability: number; // 0-1
  requiredAgents: string[];
  steps: string[];
  rollbackPlan: string[];
}

export interface ResolutionAttempt {
  strategyId: string;
  agentId: string;
  startTime: number;
  endTime?: number;
  success: boolean;
  result?: string;
  error?: string;
  learnings: string[];
}

export class ErrorResolutionCoordinatorAgent extends AgentBase {
  private activeErrors: Map<string, ErrorAnalysis> = new Map();
  private resolutionHistory: Map<string, ResolutionAttempt[]> = new Map();
  private knownPatterns: Map<string, ResolutionStrategy[]> = new Map();

  constructor(config: AgentConfig) {
    super(config);
    this.initializeKnownPatterns();
  }

  public getCapabilities(): string[] {
    return [
      'error_analysis',
      'multi_strategy_resolution',
      'collaborative_problem_solving',
      'pattern_recognition',
      'rollback_management',
      'learning_integration'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Error Resolution Coordinator, responsible for analyzing errors that other agents cannot resolve independently and orchestrating collaborative resolution approaches.

CORE RESPONSIBILITIES:
1. Analyze persistent errors comprehensively (pattern matching, root cause analysis, dependency checking)
2. Generate multiple resolution hypotheses and strategies
3. Coordinate multi-agent problem solving with appropriate specialists
4. Leverage external knowledge sources when needed
5. Implement progressive resolution with rollback capabilities
6. Learn from error patterns and successful solutions

RESOLUTION APPROACH:
- Collect comprehensive error information and context
- Generate multiple hypotheses about error causes
- Develop specific resolution strategies for each hypothesis
- Engage appropriate agents based on error type and strategy
- Execute solutions progressively (least invasive first)
- Document learnings for future reference

Your goal is to resolve errors efficiently without getting stuck in repetitive loops.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // Parse error information from context
      const errorInfo = this.parseErrorContext(context);

      // Analyze the error comprehensively
      const analysis = await this.analyzeError(errorInfo, context);

      // Generate resolution strategies
      const strategies = await this.generateResolutionStrategies(analysis);

      // Execute resolution attempts
      const resolutionResult = await this.executeResolutionStrategies(analysis, strategies, context);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 1200; // High overhead for coordination

      return this.createSuccessResponse(
        resolutionResult.report,
        tokensUsed,
        executionTime,
        resolutionResult.suggestions,
        {
          errorResolved: resolutionResult.success,
          strategiesAttempted: resolutionResult.strategiesAttempted,
          finalStrategy: resolutionResult.successfulStrategy,
          learningsGenerated: resolutionResult.learnings.length
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Error resolution coordination failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private parseErrorContext(context: AgentContext): {
    originalError: string;
    failedAgent: string;
    previousAttempts: string[];
    codeContext?: string;
    environment?: string;
  } {
    // Extract error information from task description and metadata
    const task = context.task;
    const metadata = context.metadata || {};

    return {
      originalError: metadata.originalError || task,
      failedAgent: metadata.failedAgent || 'unknown',
      previousAttempts: metadata.previousAttempts || [],
      codeContext: context.codeContext,
      environment: metadata.environment
    };
  }

  private async analyzeError(errorInfo: any, context: AgentContext): Promise<ErrorAnalysis> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const analysis: ErrorAnalysis = {
      errorId,
      originalError: errorInfo.originalError,
      errorType: this.classifyErrorType(errorInfo.originalError),
      severity: this.assessErrorSeverity(errorInfo.originalError, errorInfo.previousAttempts),
      context: context.codeContext || 'No context provided',
      previousAttempts: errorInfo.previousAttempts,
      affectedComponents: this.identifyAffectedComponents(errorInfo.originalError, context),
      estimatedComplexity: this.estimateErrorComplexity(errorInfo.originalError, errorInfo.previousAttempts)
    };

    // Perform root cause analysis
    analysis.rootCause = await this.performRootCauseAnalysis(analysis);

    // Store for tracking
    this.activeErrors.set(errorId, analysis);

    return analysis;
  }

  private classifyErrorType(error: string): ErrorAnalysis['errorType'] {
    const errorLower = error.toLowerCase();

    if (errorLower.includes('syntax') || errorLower.includes('parse')) return 'syntax';
    if (errorLower.includes('import') || errorLower.includes('module') || errorLower.includes('dependency')) return 'dependency';
    if (errorLower.includes('config') || errorLower.includes('setting')) return 'configuration';
    if (errorLower.includes('api') || errorLower.includes('integration')) return 'integration';
    if (errorLower.includes('runtime') || errorLower.includes('execution')) return 'runtime';

    return 'logic';
  }

  private assessErrorSeverity(error: string, previousAttempts: string[]): ErrorAnalysis['severity'] {
    const attemptCount = previousAttempts.length;
    const errorLower = error.toLowerCase();

    // Critical indicators
    if (errorLower.includes('critical') || errorLower.includes('fatal') || errorLower.includes('crash')) {
      return 'critical';
    }

    // High severity based on attempts or error type
    if (attemptCount > 3 || errorLower.includes('system') || errorLower.includes('security')) {
      return 'high';
    }

    // Medium severity
    if (attemptCount > 1 || errorLower.includes('warning') || errorLower.includes('deprecated')) {
      return 'medium';
    }

    return 'low';
  }

  private identifyAffectedComponents(error: string, context: AgentContext): string[] {
    const components: string[] = [];

    // Extract from file names in context
    if (context.files) {
      components.push(...context.files);
    }

    // Extract from error message
    const fileMatches = error.match(/[\w-]+\.(ts|js|tsx|jsx|py|java|cpp)/g);
    if (fileMatches) {
      components.push(...fileMatches);
    }

    // Extract module/package names
    const moduleMatches = error.match(/module ['"]([^'"]+)['"]/g);
    if (moduleMatches) {
      components.push(...moduleMatches.map(m => m.replace(/module ['"]([^'"]+)['"]/, '$1')));
    }

    return [...new Set(components)]; // Remove duplicates
  }

  private estimateErrorComplexity(error: string, previousAttempts: string[]): number {
    let complexity = 3; // Base complexity

    // Increase based on attempt count
    complexity += Math.min(previousAttempts.length, 5);

    // Increase based on error indicators
    const errorLower = error.toLowerCase();
    if (errorLower.includes('circular') || errorLower.includes('recursive')) complexity += 3;
    if (errorLower.includes('async') || errorLower.includes('race condition')) complexity += 2;
    if (errorLower.includes('memory') || errorLower.includes('performance')) complexity += 2;
    if (errorLower.includes('integration') || errorLower.includes('api')) complexity += 2;

    return Math.min(complexity, 10);
  }

  private async performRootCauseAnalysis(analysis: ErrorAnalysis): Promise<string> {
    // Pattern matching against known issues
    const knownPattern = this.findKnownPattern(analysis.originalError);
    if (knownPattern) {
      return `Known pattern: ${knownPattern}`;
    }

    // Analyze based on error type
    switch (analysis.errorType) {
      case 'syntax':
        return 'Likely caused by incorrect syntax, missing punctuation, or malformed code structure';
      case 'dependency':
        return 'Likely caused by missing dependencies, version conflicts, or import path issues';
      case 'configuration':
        return 'Likely caused by incorrect configuration settings or missing environment variables';
      case 'integration':
        return 'Likely caused by API changes, network issues, or interface mismatches';
      case 'runtime':
        return 'Likely caused by null references, type mismatches, or unexpected data';
      case 'logic':
        return 'Likely caused by incorrect algorithm implementation or business logic errors';
      default:
        return 'Root cause analysis inconclusive - requires deeper investigation';
    }
  }

  private findKnownPattern(error: string): string | null {
    // Check against known error patterns
    const patterns = [
      { pattern: /cannot find module/i, cause: 'Missing dependency or incorrect import path' },
      { pattern: /unexpected token/i, cause: 'Syntax error or malformed code' },
      { pattern: /cannot read property.*of undefined/i, cause: 'Null reference error' },
      { pattern: /circular dependency/i, cause: 'Circular import structure' },
      { pattern: /port.*already in use/i, cause: 'Port conflict - another process using the port' },
      { pattern: /permission denied/i, cause: 'File system permissions or access rights issue' }
    ];

    for (const { pattern, cause } of patterns) {
      if (pattern.test(error)) {
        return cause;
      }
    }

    return null;
  }

  private async generateResolutionStrategies(analysis: ErrorAnalysis): Promise<ResolutionStrategy[]> {
    const strategies: ResolutionStrategy[] = [];

    // Generate strategies based on error type
    switch (analysis.errorType) {
      case 'syntax':
        strategies.push(...this.generateSyntaxStrategies(analysis));
        break;
      case 'dependency':
        strategies.push(...this.generateDependencyStrategies(analysis));
        break;
      case 'configuration':
        strategies.push(...this.generateConfigurationStrategies(analysis));
        break;
      case 'integration':
        strategies.push(...this.generateIntegrationStrategies(analysis));
        break;
      case 'runtime':
        strategies.push(...this.generateRuntimeStrategies(analysis));
        break;
      case 'logic':
        strategies.push(...this.generateLogicStrategies(analysis));
        break;
    }

    // Add general strategies
    strategies.push(...this.generateGeneralStrategies(analysis));

    // Sort by success probability
    return strategies.sort((a, b) => b.successProbability - a.successProbability);
  }

  private generateSyntaxStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'syntax_direct_fix',
        name: 'Direct Syntax Fix',
        description: 'Fix syntax errors directly in the code',
        approach: 'direct_fix',
        estimatedEffort: 2,
        successProbability: 0.85,
        requiredAgents: ['junior', 'midlevel'],
        steps: [
          'Identify exact syntax error location',
          'Apply appropriate syntax correction',
          'Validate syntax with linter',
          'Test basic functionality'
        ],
        rollbackPlan: ['Restore original code', 'Document failed attempt']
      },
      {
        id: 'syntax_rewrite_section',
        name: 'Rewrite Problematic Section',
        description: 'Rewrite the problematic code section with correct syntax',
        approach: 'refactor',
        estimatedEffort: 4,
        successProbability: 0.75,
        requiredAgents: ['midlevel', 'senior'],
        steps: [
          'Identify problematic code section',
          'Understand intended functionality',
          'Rewrite with correct syntax',
          'Preserve original behavior',
          'Add comments for clarity'
        ],
        rollbackPlan: ['Restore original implementation', 'Document approach taken']
      }
    ];
  }

  private generateDependencyStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'dependency_install_missing',
        name: 'Install Missing Dependencies',
        description: 'Install missing packages and dependencies',
        approach: 'configuration_change',
        estimatedEffort: 3,
        successProbability: 0.8,
        requiredAgents: ['junior'],
        steps: [
          'Identify missing dependencies from error message',
          'Install required packages',
          'Update package.json/requirements.txt',
          'Clear cache and reinstall if needed',
          'Verify import resolution'
        ],
        rollbackPlan: ['Remove installed packages', 'Restore original package files']
      },
      {
        id: 'dependency_fix_imports',
        name: 'Fix Import Paths',
        description: 'Correct import paths and module references',
        approach: 'direct_fix',
        estimatedEffort: 2,
        successProbability: 0.9,
        requiredAgents: ['junior', 'midlevel'],
        steps: [
          'Analyze current import statements',
          'Check actual file locations',
          'Update import paths to match file structure',
          'Use relative vs absolute imports appropriately',
          'Test all imports resolve correctly'
        ],
        rollbackPlan: ['Restore original import statements']
      },
      {
        id: 'dependency_version_resolution',
        name: 'Resolve Version Conflicts',
        description: 'Resolve dependency version conflicts',
        approach: 'configuration_change',
        estimatedEffort: 5,
        successProbability: 0.7,
        requiredAgents: ['midlevel', 'senior'],
        steps: [
          'Identify conflicting package versions',
          'Research compatible version combinations',
          'Update to compatible versions',
          'Test for breaking changes',
          'Update code if API changes exist'
        ],
        rollbackPlan: ['Restore original package versions', 'Revert code changes']
      }
    ];
  }

  private generateConfigurationStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'config_fix_settings',
        name: 'Fix Configuration Settings',
        description: 'Correct configuration files and settings',
        approach: 'configuration_change',
        estimatedEffort: 3,
        successProbability: 0.8,
        requiredAgents: ['junior', 'midlevel'],
        steps: [
          'Identify incorrect configuration values',
          'Research correct configuration format',
          'Update configuration files',
          'Validate configuration syntax',
          'Test application with new config'
        ],
        rollbackPlan: ['Restore original configuration files']
      },
      {
        id: 'config_environment_setup',
        name: 'Setup Environment Variables',
        description: 'Configure missing environment variables',
        approach: 'configuration_change',
        estimatedEffort: 2,
        successProbability: 0.85,
        requiredAgents: ['junior'],
        steps: [
          'Identify missing environment variables',
          'Create .env file or update existing',
          'Set appropriate default values',
          'Document environment requirements',
          'Test application startup'
        ],
        rollbackPlan: ['Remove added environment variables']
      }
    ];
  }

  private generateIntegrationStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'integration_api_fix',
        name: 'Fix API Integration',
        description: 'Resolve API integration issues',
        approach: 'direct_fix',
        estimatedEffort: 4,
        successProbability: 0.75,
        requiredAgents: ['midlevel', 'senior'],
        steps: [
          'Review API documentation for changes',
          'Test API endpoints manually',
          'Update API call implementation',
          'Handle new response formats',
          'Add proper error handling'
        ],
        rollbackPlan: ['Restore original API implementation', 'Document API changes']
      },
      {
        id: 'integration_service_abstraction',
        name: 'Create Service Abstraction',
        description: 'Create service abstraction layer for better integration',
        approach: 'workaround',
        estimatedEffort: 3,
        successProbability: 0.9,
        requiredAgents: ['midlevel'],
        steps: [
          'Create service interface abstraction',
          'Implement service adapter pattern',
          'Configure service in environment',
          'Update integration code to use abstraction',
          'Add configuration for different environments'
        ],
        rollbackPlan: ['Remove service abstraction', 'Restore direct integration']
      }
    ];
  }

  private generateRuntimeStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'runtime_null_checks',
        name: 'Add Null Safety Checks',
        description: 'Add null and undefined checks to prevent runtime errors',
        approach: 'direct_fix',
        estimatedEffort: 2,
        successProbability: 0.85,
        requiredAgents: ['junior', 'midlevel'],
        steps: [
          'Identify variables that could be null/undefined',
          'Add appropriate null checks',
          'Provide default values where appropriate',
          'Add error handling for null cases',
          'Test edge cases'
        ],
        rollbackPlan: ['Remove added null checks', 'Restore original logic']
      },
      {
        id: 'runtime_error_handling',
        name: 'Improve Error Handling',
        description: 'Add comprehensive error handling and recovery',
        approach: 'refactor',
        estimatedEffort: 4,
        successProbability: 0.8,
        requiredAgents: ['midlevel', 'senior'],
        steps: [
          'Wrap risky operations in try-catch blocks',
          'Add specific error types and messages',
          'Implement graceful error recovery',
          'Add logging for debugging',
          'Test error scenarios'
        ],
        rollbackPlan: ['Remove error handling wrapper', 'Restore original flow']
      }
    ];
  }

  private generateLogicStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'logic_algorithm_fix',
        name: 'Fix Algorithm Logic',
        description: 'Correct logical errors in algorithm implementation',
        approach: 'refactor',
        estimatedEffort: 6,
        successProbability: 0.7,
        requiredAgents: ['midlevel', 'senior'],
        steps: [
          'Analyze intended algorithm behavior',
          'Identify logical errors or edge cases',
          'Redesign algorithm flow',
          'Implement corrected logic',
          'Add comprehensive tests'
        ],
        rollbackPlan: ['Restore original algorithm', 'Document failed approach']
      },
      {
        id: 'logic_alternative_approach',
        name: 'Alternative Implementation',
        description: 'Implement alternative approach to achieve the same goal',
        approach: 'alternative_implementation',
        estimatedEffort: 7,
        successProbability: 0.8,
        requiredAgents: ['senior', 'architect'],
        steps: [
          'Research alternative approaches',
          'Design new implementation strategy',
          'Implement alternative solution',
          'Ensure same functionality and performance',
          'Migrate from old to new implementation'
        ],
        rollbackPlan: ['Restore original implementation', 'Document alternative approach']
      }
    ];
  }

  private generateGeneralStrategies(analysis: ErrorAnalysis): ResolutionStrategy[] {
    return [
      {
        id: 'general_research_solution',
        name: 'Research Similar Issues',
        description: 'Research similar issues and known solutions',
        approach: 'direct_fix',
        estimatedEffort: 3,
        successProbability: 0.6,
        requiredAgents: ['researcher'],
        steps: [
          'Search for similar error patterns',
          'Review documentation and forums',
          'Identify common solutions',
          'Adapt solution to current context',
          'Test proposed solution'
        ],
        rollbackPlan: ['Document research findings']
      },
      {
        id: 'general_gradual_rollback',
        name: 'Gradual Rollback and Rebuild',
        description: 'Gradually rollback changes and rebuild functionality',
        approach: 'refactor',
        estimatedEffort: 8,
        successProbability: 0.9,
        requiredAgents: ['senior', 'architect'],
        steps: [
          'Identify last known working state',
          'Gradually rollback changes',
          'Test at each rollback point',
          'Identify breaking change',
          'Rebuild with correct implementation'
        ],
        rollbackPlan: ['Return to current state', 'Document rollback points']
      }
    ];
  }

  private async executeResolutionStrategies(
    analysis: ErrorAnalysis,
    strategies: ResolutionStrategy[],
    context: AgentContext
  ): Promise<{
    success: boolean;
    report: string;
    suggestions: string[];
    strategiesAttempted: number;
    successfulStrategy?: string;
    learnings: string[];
  }> {
    const attempts: ResolutionAttempt[] = [];
    let successfulStrategy: string | undefined;
    const learnings: string[] = [];

    for (let i = 0; i < Math.min(strategies.length, 3); i++) {
      const strategy = strategies[i];

      const attempt: ResolutionAttempt = {
        strategyId: strategy.id,
        agentId: strategy.requiredAgents[0], // Use primary agent
        startTime: Date.now(),
        success: false,
        learnings: []
      };

      try {
        // Simulate strategy execution (in real implementation, this would delegate to actual agents)
        const result = await this.executeStrategy(strategy, analysis, context);

        attempt.endTime = Date.now();
        attempt.success = result.success;
        attempt.result = result.result;
        attempt.learnings = result.learnings;

        if (result.success) {
          successfulStrategy = strategy.id;
          learnings.push(`Strategy '${strategy.name}' successfully resolved the error`);
          learnings.push(...result.learnings);
          break;
        } else {
          learnings.push(`Strategy '${strategy.name}' failed: ${result.error}`);
          attempt.error = result.error;
        }

      } catch (error) {
        attempt.endTime = Date.now();
        attempt.error = error instanceof Error ? error.message : String(error);
        learnings.push(`Strategy '${strategy.name}' threw exception: ${attempt.error}`);
      }

      attempts.push(attempt);
    }

    // Store resolution history
    this.resolutionHistory.set(analysis.errorId, attempts);

    // Update knowledge base with learnings
    this.updateKnowledgeBase(analysis, attempts, learnings);

    const success = successfulStrategy !== undefined;
    const report = this.generateResolutionReport(analysis, attempts, success, successfulStrategy);
    const suggestions = this.generateResolutionSuggestions(analysis, attempts, success);

    return {
      success,
      report,
      suggestions,
      strategiesAttempted: attempts.length,
      successfulStrategy,
      learnings
    };
  }

  private async executeStrategy(
    strategy: ResolutionStrategy,
    analysis: ErrorAnalysis,
    context: AgentContext
  ): Promise<{
    success: boolean;
    result?: string;
    error?: string;
    learnings: string[];
  }> {
    // This is a simplified simulation of strategy execution
    // In real implementation, this would delegate to actual agents

    const learnings: string[] = [];

    // Simulate different success rates based on strategy
    const random = Math.random();
    const success = random < strategy.successProbability;

    if (success) {
      return {
        success: true,
        result: `Strategy '${strategy.name}' executed successfully. ${strategy.description}`,
        learnings: [
          `Applied ${strategy.approach} approach`,
          `Completed all ${strategy.steps.length} execution steps`,
          'Validated solution effectiveness'
        ]
      };
    } else {
      return {
        success: false,
        error: `Strategy '${strategy.name}' failed during execution`,
        learnings: [
          `Strategy had ${(strategy.successProbability * 100).toFixed(0)}% estimated success rate`,
          'Consider alternative approach or additional context',
          'May require human intervention'
        ]
      };
    }
  }

  private updateKnowledgeBase(analysis: ErrorAnalysis, attempts: ResolutionAttempt[], learnings: string[]): void {
    // Update known patterns with successful strategies
    const successfulAttempts = attempts.filter(a => a.success);
    if (successfulAttempts.length > 0) {
      const errorPattern = analysis.originalError.substring(0, 100); // First 100 chars as pattern key
      const existingStrategies = this.knownPatterns.get(errorPattern) || [];

      // Add successful strategies to known patterns
      successfulAttempts.forEach(attempt => {
        const strategy = this.findStrategyById(attempt.strategyId);
        if (strategy && !existingStrategies.find(s => s.id === strategy.id)) {
          existingStrategies.push(strategy);
        }
      });

      this.knownPatterns.set(errorPattern, existingStrategies);
    }

    // Store learnings for future reference (in real implementation, this would persist to database)
    console.log('Resolution learnings:', learnings);
  }

  private findStrategyById(strategyId: string): ResolutionStrategy | null {
    // This would search through all possible strategies
    // For now, return null as strategies are generated dynamically
    return null;
  }

  private generateResolutionReport(
    analysis: ErrorAnalysis,
    attempts: ResolutionAttempt[],
    success: boolean,
    successfulStrategy?: string
  ): string {
    return `[ERROR RESOLUTION COORDINATOR REPORT]

ERROR ANALYSIS:
- Error ID: ${analysis.errorId}
- Error Type: ${analysis.errorType}
- Severity: ${analysis.severity}
- Complexity: ${analysis.estimatedComplexity}/10
- Root Cause: ${analysis.rootCause}

AFFECTED COMPONENTS:
${analysis.affectedComponents.map(comp => `- ${comp}`).join('\n')}

RESOLUTION ATTEMPTS:
${attempts.map((attempt, index) => `
${index + 1}. Strategy: ${attempt.strategyId}
   Agent: ${attempt.agentId}
   Duration: ${attempt.endTime ? ((attempt.endTime - attempt.startTime) / 1000).toFixed(2) + 's' : 'In progress'}
   Result: ${attempt.success ? '✅ SUCCESS' : '❌ FAILED'}
   ${attempt.error ? `Error: ${attempt.error}` : ''}
   ${attempt.result ? `Result: ${attempt.result}` : ''}
`).join('')}

RESOLUTION STATUS: ${success ? '✅ RESOLVED' : '❌ UNRESOLVED'}
${successfulStrategy ? `Successful Strategy: ${successfulStrategy}` : ''}

PREVIOUS ATTEMPTS:
${analysis.previousAttempts.map((attempt, index) => `${index + 1}. ${attempt}`).join('\n')}

LEARNINGS CAPTURED:
- Total resolution attempts: ${attempts.length}
- Knowledge base updated with findings
- Pattern recognition improved for similar errors
${success ? '- Successful resolution strategy documented' : '- Failed attempts documented for future reference'}

NEXT STEPS:
${success ?
  '- Verify resolution effectiveness\n- Monitor for regression\n- Document solution for team' :
  '- Consider escalation to human developer\n- Gather additional context\n- Try alternative diagnostic approaches'
}

[END RESOLUTION REPORT]`;
  }

  private generateResolutionSuggestions(
    analysis: ErrorAnalysis,
    attempts: ResolutionAttempt[],
    success: boolean
  ): string[] {
    const suggestions: string[] = [];

    if (success) {
      suggestions.push('Resolution successful - monitor for any side effects or regressions');
      suggestions.push('Document the successful approach for similar future issues');
      suggestions.push('Consider creating automated tests to prevent similar errors');
    } else {
      suggestions.push('Consider gathering additional context or error details');
      suggestions.push('May require human developer intervention for complex cases');
      suggestions.push('Review error patterns and consider system-wide fixes');

      if (analysis.severity === 'critical') {
        suggestions.push('Critical error - consider immediate escalation');
      }

      if (attempts.length >= 3) {
        suggestions.push('Multiple strategies failed - may indicate deeper system issues');
      }
    }

    suggestions.push('Update error handling to prevent similar issues in future');
    suggestions.push('Consider improving error messages for better diagnosis');

    return suggestions;
  }

  private initializeKnownPatterns(): void {
    // Initialize with some common error patterns and their solutions
    this.knownPatterns.set('cannot find module', [
      {
        id: 'install_missing_module',
        name: 'Install Missing Module',
        description: 'Install the missing module using package manager',
        approach: 'configuration_change',
        estimatedEffort: 2,
        successProbability: 0.9,
        requiredAgents: ['junior'],
        steps: ['Identify missing module', 'Install using npm/yarn', 'Verify installation'],
        rollbackPlan: ['Uninstall if causing conflicts']
      }
    ]);

    this.knownPatterns.set('unexpected token', [
      {
        id: 'fix_syntax_error',
        name: 'Fix Syntax Error',
        description: 'Correct the syntax error in the code',
        approach: 'direct_fix',
        estimatedEffort: 1,
        successProbability: 0.95,
        requiredAgents: ['junior'],
        steps: ['Locate syntax error', 'Fix punctuation/brackets', 'Validate syntax'],
        rollbackPlan: ['Restore original code']
      }
    ]);
  }

  // Public methods for integration with other components
  public async resolveError(
    originalError: string,
    failedAgent: string,
    previousAttempts: string[],
    context: AgentContext
  ): Promise<AgentResponse> {
    const enhancedContext: AgentContext = {
      ...context,
      task: `Resolve error: ${originalError}`,
      metadata: {
        ...context.metadata,
        originalError,
        failedAgent,
        previousAttempts
      }
    };

    return this.execute(enhancedContext);
  }

  public getErrorHistory(errorId: string): ResolutionAttempt[] {
    return this.resolutionHistory.get(errorId) || [];
  }

  public getActiveErrors(): ErrorAnalysis[] {
    return Array.from(this.activeErrors.values());
  }

  public clearResolvedError(errorId: string): void {
    this.activeErrors.delete(errorId);
  }
}