"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, Send, User, Clock, CheckCircle, RefreshCw, Trash2, Info, AlertTriangle, AlertCircle, Users, Zap, ZapOff, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAgentChatSync } from "@/hooks/useAgentChatSync"
import type { AgentChatMessage as AgentMessage } from "@/types/chat"
import { getSystemMessageStyling, getSystemMessageIcon } from "@/utils/system-message-utils"
import { useAgentPresence } from "@/hooks/useAgentPresence"
import AgentStatusWidget from "./agent-status-widget"
import LivePresencePanel from "./live-presence-panel"
import SyncStatusIndicator from "./sync-status-indicator"

type MessageRole = "user" | "agent" | "system"
type MessageStatus = "sending" | "sent" | "processing" | "completed" | "error"

interface AgentChatPanelProps {
  onClose: () => void
  onDetach?: () => void
}

export default function AgentChatPanel({ onClose, onDetach }: AgentChatPanelProps) {
  const [input, setInput] = useState("")
  const [showPresencePanel, setShowPresencePanel] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const {
    messages,
    isProcessing,
    streamingMessageId,
    isLoaded,
    enableStreaming,
    setEnableStreaming,
    sendMessage,
    clearMessages
  } = useAgentChatSync()
  const { getWorkingSummary, activeTaskCount } = useAgentPresence()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const handleSendMessage = async () => {
    if (!input.trim()) return

    await sendMessage(input)
    setInput("")
    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }
  }

  const handleRetryMessage = async (content: string) => {
    await sendMessage(content)
  }

  const handleClearHistory = async () => {
    if (confirm("Are you sure you want to clear the chat history? This action cannot be undone.")) {
      await clearMessages()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Auto-resize textarea
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)

    // Auto-resize
    const textarea = e.target
    textarea.style.height = 'auto'
    const newHeight = Math.min(textarea.scrollHeight, 200) // Max height 200px
    textarea.style.height = `${newHeight}px`
  }

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const getMessageIcon = (role: MessageRole, agentType?: string) => {
    if (role === "user") return <User className="h-4 w-4" />
    if (role === "agent") return <Bot className="h-4 w-4" />
    return <Bot className="h-4 w-4" />
  }

  const getStatusIcon = (status: MessageStatus) => {
    switch (status) {
      case "sending":
        return <Clock className="h-3 w-3 animate-spin" />
      case "processing":
        return <Clock className="h-3 w-3 animate-pulse" />
      case "completed":
        return <CheckCircle className="h-3 w-3 text-green-600" />
      case "error":
        return <CheckCircle className="h-3 w-3 text-red-600" />
      default:
        return null
    }
  }

  const getAgentBadgeColor = (agentType?: string) => {
    switch (agentType) {
      case "micromanager":
        return "bg-blue-500/10 text-blue-600 border-blue-500/20"
      case "designer":
        return "bg-purple-500/10 text-purple-600 border-purple-500/20"
      case "developer":
      case "midlevel":
      case "senior":
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case "intern":
      case "junior":
        return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
      case "tester":
        return "bg-orange-500/10 text-orange-600 border-orange-500/20"
      case "architect":
        return "bg-indigo-500/10 text-indigo-600 border-indigo-500/20"
      case "researcher":
        return "bg-cyan-500/10 text-cyan-600 border-cyan-500/20"
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    }
  }

  const getAgentIcon = (agentType?: string) => {
    switch (agentType) {
      case "micromanager":
        return "🧠"
      case "intern":
        return "🛠️"
      case "junior":
        return "📦"
      case "midlevel":
        return "⚙️"
      case "senior":
        return "🧱"
      case "designer":
        return "🎨"
      case "architect":
        return "🏗️"
      case "researcher":
        return "📘"
      case "tester":
        return "🧪"
      default:
        return "🤖"
    }
  }

  const getSystemMessageIcon = (severity: string) => {
    switch (severity) {
      case "error":
        return <AlertCircle className="h-3 w-3 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />
      case "success":
        return <CheckCircle className="h-3 w-3 text-green-500" />
      case "info":
      default:
        return <Info className="h-3 w-3 text-blue-500" />
    }
  }

  // Add console log to verify header is rendered
  console.log("✅ Agent Chat Header is rendered")

  return (
    <Card className="flex flex-col h-full border-0 rounded-none bg-background">
      <CardHeader className="border-b border-editor-border p-4 bg-editor-sidebar-bg/30 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-editor-highlight" />
              <span className="font-semibold text-base">Agent Chat</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs flex items-center gap-1 bg-blue-500/10 text-blue-600 border-blue-500/20">
                🧠 Micromanager
              </Badge>
            {streamingMessageId && (
              <Badge variant="outline" className="text-xs bg-green-500/10 text-green-600 border-green-500/20 animate-pulse">
                <Zap className="h-3 w-3 mr-1" />
                Streaming
              </Badge>
            )}
            {activeTaskCount > 0 && (
              <Badge variant="outline" className="text-xs bg-blue-500/10 text-blue-600 border-blue-500/20">
                {activeTaskCount} active
              </Badge>
            )}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <SyncStatusIndicator />
            <Badge
              variant="outline"
              className={cn(
                "text-xs cursor-pointer transition-colors",
                enableStreaming
                  ? "bg-green-500/10 text-green-600 border-green-500/20 hover:bg-green-500/20"
                  : "bg-gray-500/10 text-gray-600 border-gray-500/20 hover:bg-gray-500/20"
              )}
              onClick={() => setEnableStreaming(!enableStreaming)}
              title={enableStreaming ? "Disable real-time streaming" : "Enable real-time streaming"}
            >
              {enableStreaming ? <Zap className="h-3 w-3 mr-1" /> : <ZapOff className="h-3 w-3 mr-1" />}
              {enableStreaming ? "Stream" : "No Stream"}
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-6 w-6 text-muted-foreground hover:text-foreground",
                showPresencePanel && "bg-accent text-accent-foreground"
              )}
              onClick={() => setShowPresencePanel(!showPresencePanel)}
              title="Toggle agent presence panel"
            >
              <Users className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-muted-foreground hover:text-foreground"
              onClick={handleClearHistory}
              title="Clear chat history"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
            {onDetach && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={onDetach}
                title="Open in new window"
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden p-0">
        {showPresencePanel && (
          <div className="border-b border-editor-border">
            <LivePresencePanel showOnlyActive={true} />
          </div>
        )}
        <ScrollArea className="h-full">
          <div className="p-3 space-y-4">
            {!isLoaded && (
              <div className="flex items-center justify-center py-4 text-muted-foreground">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                  Loading chat history...
                </div>
              </div>
            )}

            {isLoaded && messages.map((message) => {
              // Handle system messages differently
              if (message.role === "system") {
                const severity = message.metadata?.severity || "info"
                return (
                  <div key={message.id} className="flex gap-2 items-start">
                    <div className="flex-shrink-0 mt-1">
                      {getSystemMessageIcon(severity)}
                    </div>
                    <div className={cn("flex-1 text-xs", getSystemMessageStyling(severity))}>
                      <div className="font-medium mb-1">
                        {getSystemMessageIcon(message.source || "system")} System {message.source ? `(${message.source})` : ""}
                      </div>
                      <div>{message.content}</div>
                      {message.linkedToMessageId && (
                        <div className="text-xs opacity-60 mt-1">
                          ↳ Related to message above
                        </div>
                      )}
                      <div className="text-xs opacity-60 mt-1">
                        {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </div>
                    </div>
                  </div>
                )
              }

              // Handle regular user/agent messages
              return (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-3",
                    message.role === "user" ? "justify-end" : "justify-start"
                  )}
                >
                  {message.role !== "user" && (
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center text-sm">
                      {getAgentIcon(message.agentType)}
                    </div>
                  )}

                <div className={cn(
                  "max-w-[85%] space-y-1",
                  message.role === "user" ? "items-end" : "items-start"
                )}>
                  <div className={cn(
                    "rounded-lg px-4 py-3 text-sm shadow-sm",
                    message.role === "user"
                      ? "bg-editor-highlight text-editor-highlight-fg shadow-editor-highlight/20"
                      : message.role === "system"
                      ? "bg-red-50 dark:bg-red-950/50 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800/50 backdrop-blur-sm"
                      : message.status === "error"
                      ? "bg-red-50 dark:bg-red-950/50 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800/50 backdrop-blur-sm"
                      : "bg-accent/80 text-accent-foreground border border-border/50 backdrop-blur-sm"
                  )}>
                    {message.content}
                    {message.isStreaming && (
                      <span className="inline-flex items-center ml-1">
                        <span className="inline-block w-1 h-4 bg-current animate-pulse">|</span>
                        <span className="text-xs ml-1 opacity-60">
                          {message.metadata?.isRealStream ? "🔴 Live" : "⚡ Sim"}
                        </span>
                      </span>
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {message.agentType && (
                      <Badge
                        variant="outline"
                        className={cn("text-xs h-4 flex items-center gap-1", getAgentBadgeColor(message.agentType))}
                      >
                        <span className="text-xs">{getAgentIcon(message.agentType)}</span>
                        {message.agentType}
                      </Badge>
                    )}
                    <span>{message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</span>
                    {getStatusIcon(message.status)}
                    {message.status === "error" && message.role === "system" && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-4 px-1 text-xs"
                        onClick={() => {
                          // Extract original user message from error content
                          const match = message.content.match(/failed to respond: (.+)/)
                          if (match) {
                            const originalContent = match[1]
                            handleRetryMessage(originalContent)
                          }
                        }}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Retry
                      </Button>
                    )}
                  </div>
                </div>

                {message.role === "user" && (
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-editor-highlight flex items-center justify-center">
                    {getMessageIcon(message.role)}
                  </div>
                )}
                </div>
              )
            })}

            {streamingMessageId && messages.find(m => m.id === streamingMessageId && !m.content) && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center text-sm">
                  🧠
                </div>
                <div className="bg-accent text-accent-foreground rounded-lg px-3 py-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                    </div>
                    <span>Micromanager is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t border-editor-border p-3">
        <div className="w-full space-y-2">
          <div className="flex items-end w-full gap-2">
            <div className="flex-1 relative">
              <Textarea
                ref={textareaRef}
                placeholder="Describe what you'd like to accomplish... (Enter to send, Shift+Enter for new line)"
                className="min-h-[40px] max-h-[200px] bg-background border-input text-sm resize-none pr-12"
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                disabled={isProcessing}
                rows={1}
              />
              {input.length > 0 && (
                <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background/80 px-1 rounded">
                  {input.length}
                </div>
              )}
            </div>
            <Button
              size="icon"
              className="bg-editor-highlight text-editor-highlight-fg hover:bg-editor-highlight/90 mb-1"
              onClick={handleSendMessage}
              disabled={!input.trim() || isProcessing}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardFooter>

      <AgentStatusWidget />
    </Card>
  )
}
