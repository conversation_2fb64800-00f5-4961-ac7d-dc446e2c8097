// components/agents/implementation/intern-agent.ts
import { <PERSON><PERSON><PERSON>, AgentConfig, AgentContext, AgentResponse } from '../agent-base';
import { LLMRequestService, LLMMessage } from '../llm-request-service';

export class InternAgent extends AgentBase {
  private llmService: LLMRequestService;

  constructor(config: AgentConfig) {
    super(config);
    this.llmService = LLMRequestService.getInstance();
  }

  public getCapabilities(): string[] {
    return [
      'simple_tasks',
      'boilerplate_generation',
      'template_implementation',
      'basic_file_operations',
      'simple_debugging'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Intern implementation agent, responsible for handling simple, well-defined coding tasks.

CORE RESPONSIBILITIES:
1. Implement straightforward, clearly specified code following explicit instructions
2. Focus only on the context provided for your specific task
3. Try one straightforward solution, report issues if unsuccessful
4. Stick strictly to simple, well-defined tasks

CAPABILITIES:
- Single file, template-based tasks
- Boilerplate code generation
- Simple function implementations
- Basic file operations
- Following explicit patterns

ESCALATION CRITERIA:
- Complex algorithms or logic
- Multiple file interactions
- Architectural decisions
- Performance optimization

Focus on accuracy and consistency rather than innovation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // Check if task is suitable for intern level
      if (!this.isTaskSuitableForIntern(context.task)) {
        return this.createErrorResponse(
          'Task complexity exceeds Intern capabilities. Consider Junior or higher level agent.'
        );
      }

      // Prepare messages for LLM
      const messages: LLMMessage[] = [
        {
          role: 'system',
          content: this.getSystemPrompt()
        },
        {
          role: 'user',
          content: this.buildUserPrompt(context)
        }
      ];

      // Call LLM service
      const llmResponse = await this.llmService.callLLM(this.config, messages);

      const executionTime = Date.now() - startTime;

      return this.createSuccessResponse(
        llmResponse.content,
        llmResponse.tokensUsed.total,
        executionTime,
        ['Task completed with AI-generated implementation', 'Consider review by higher-level agent'],
        {
          taskType: 'simple_implementation',
          provider: llmResponse.provider,
          model: llmResponse.model,
          finishReason: llmResponse.finishReason
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Intern task failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context),
        executionTime
      );
    }
  }

  private isTaskSuitableForIntern(task: string): boolean {
    const task_lower = task.toLowerCase();

    // Tasks too complex for intern
    const complexIndicators = [
      'complex', 'algorithm', 'architecture', 'performance', 'optimization',
      'multiple files', 'integration', 'database', 'api design'
    ];

    return !complexIndicators.some(indicator => task_lower.includes(indicator));
  }

  private buildUserPrompt(context: AgentContext): string {
    let prompt = `Task: ${context.task}\n\n`;

    if (context.codeContext) {
      prompt += `Code Context:\n${context.codeContext}\n\n`;
    }

    if (context.files && context.files.length > 0) {
      prompt += `Related Files: ${context.files.join(', ')}\n\n`;
    }

    if (context.rules && context.rules.length > 0) {
      prompt += `Rules to Follow:\n${context.rules.map(rule => `- ${rule}`).join('\n')}\n\n`;
    }

    prompt += `Please provide a simple, clean implementation for this task. Focus on:
- Clear, readable code
- Basic functionality
- Following established patterns
- Proper error handling where needed

Provide only the code implementation without extensive explanations.`;

    return prompt;
  }
}
